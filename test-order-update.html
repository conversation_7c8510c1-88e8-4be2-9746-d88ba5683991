<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Order Status Update</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .info {
            color: blue;
        }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Order Status Update Test</h1>
    
    <div class="test-section">
        <h3>Authentication</h3>
        <button onclick="signInAsAdmin()">Sign In as Admin</button>
        <button onclick="signOut()">Sign Out</button>
        <div id="auth-status"></div>
    </div>
    
    <div class="test-section">
        <h3>Test Order Status Update</h3>
        <p>Order ID: <span id="test-order-id">097c07a8-bedd-40f7-b78d-bf4d0410c77c</span></p>
        <button onclick="updateOrderStatus('pending')">Set to Pending</button>
        <button onclick="updateOrderStatus('processing')">Set to Processing</button>
        <button onclick="updateOrderStatus('shipped')">Set to Shipped</button>
        <button onclick="updateOrderStatus('delivered')">Set to Delivered</button>
        <button onclick="updateOrderStatus('cancelled')">Set to Cancelled</button>
    </div>
    
    <div class="test-section">
        <h3>Current Order Status</h3>
        <button onclick="checkOrderStatus()">Check Current Status</button>
        <div id="current-status"></div>
    </div>
    
    <div class="test-section">
        <h3>Log</h3>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log"></div>
    </div>

    <script>
        const supabaseUrl = 'https://olnhzuxguxjwcbsxytju.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9sbmh6dXhndXhqd2Nic3h5dGp1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2MTc3MDEsImV4cCI6MjA2NDE5MzcwMX0.zKcLCo1k2G4pwOyV2lnfKTkPeKdMQV1W1SKd81hWTxQ';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function signInAsAdmin() {
            try {
                log('Attempting to sign in as admin...');
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: 'admin123' // You'll need to use the actual password
                });
                
                if (error) {
                    log(`Sign in error: ${error.message}`, 'error');
                    return;
                }
                
                log('Sign in successful!', 'success');
                updateAuthStatus();
            } catch (error) {
                log(`Sign in exception: ${error.message}`, 'error');
            }
        }
        
        async function signOut() {
            try {
                const { error } = await supabase.auth.signOut();
                if (error) {
                    log(`Sign out error: ${error.message}`, 'error');
                } else {
                    log('Signed out successfully', 'success');
                    updateAuthStatus();
                }
            } catch (error) {
                log(`Sign out exception: ${error.message}`, 'error');
            }
        }
        
        async function updateAuthStatus() {
            try {
                const { data: { user } } = await supabase.auth.getUser();
                const statusDiv = document.getElementById('auth-status');
                
                if (user) {
                    // Check if user is admin
                    const { data: profile } = await supabase
                        .from('profiles')
                        .select('is_admin, full_name')
                        .eq('id', user.id)
                        .single();
                    
                    statusDiv.innerHTML = `
                        <div class="success">
                            Signed in as: ${user.email}<br>
                            Name: ${profile?.full_name || 'N/A'}<br>
                            Admin: ${profile?.is_admin ? 'Yes' : 'No'}
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = '<div class="error">Not signed in</div>';
                }
            } catch (error) {
                log(`Auth status error: ${error.message}`, 'error');
            }
        }
        
        async function updateOrderStatus(newStatus) {
            try {
                const orderId = document.getElementById('test-order-id').textContent;
                log(`Attempting to update order ${orderId} to status: ${newStatus}`);
                
                const { data, error } = await supabase
                    .from('orders')
                    .update({ 
                        status: newStatus,
                        updated_at: new Date().toISOString()
                    })
                    .eq('id', orderId)
                    .select()
                    .single();
                
                if (error) {
                    log(`Update error: ${JSON.stringify(error)}`, 'error');
                    return;
                }
                
                log(`Successfully updated order status to: ${newStatus}`, 'success');
                log(`Updated order data: ${JSON.stringify(data)}`, 'info');
                checkOrderStatus();
            } catch (error) {
                log(`Update exception: ${error.message}`, 'error');
            }
        }
        
        async function checkOrderStatus() {
            try {
                const orderId = document.getElementById('test-order-id').textContent;
                log(`Checking current status for order: ${orderId}`);
                
                const { data, error } = await supabase
                    .from('orders')
                    .select('id, status, updated_at')
                    .eq('id', orderId)
                    .single();
                
                if (error) {
                    log(`Check status error: ${JSON.stringify(error)}`, 'error');
                    return;
                }
                
                const statusDiv = document.getElementById('current-status');
                statusDiv.innerHTML = `
                    <div class="info">
                        <strong>Current Status:</strong> ${data.status}<br>
                        <strong>Last Updated:</strong> ${new Date(data.updated_at).toLocaleString()}
                    </div>
                `;
                
                log(`Current order status: ${data.status}`, 'success');
            } catch (error) {
                log(`Check status exception: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        updateAuthStatus();
        checkOrderStatus();
    </script>
</body>
</html>
